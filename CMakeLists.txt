cmake_minimum_required(VERSION 3.16)

project(Qt5CommunicationFramework VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5组件
find_package(Qt5 5.14.2 REQUIRED COMPONENTS
    Core
    Widgets
    Network
    Concurrent
)

# 设置Qt5自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 通信框架库
add_library(CommunicationFramework STATIC
    communication_framework.h
    communication_framework.cpp
)

target_link_libraries(CommunicationFramework
    Qt5::Core
    Qt5::Widgets
    Qt5::Network
    Qt5::Concurrent
)

# 完整示例可执行文件
add_executable(ExampleComplete
    example_complete.cpp
)

target_link_libraries(ExampleComplete
    CommunicationFramework
    Qt5::Core
    Qt5::Widgets
    Qt5::Network
    Qt5::Concurrent
)

# 替代实现示例可执行文件
add_executable(AlternativeImplementations
    alternative_implementations.cpp
)

target_link_libraries(AlternativeImplementations
    Qt5::Core
    Qt5::Widgets
    Qt5::Network
    Qt5::Concurrent
)

# Qt5.14.2兼容性测试可执行文件
add_executable(Qt5CompatibilityTest
    qt5_compatibility_test.cpp
)

target_link_libraries(Qt5CompatibilityTest
    CommunicationFramework
    Qt5::Core
    Qt5::Widgets
    Qt5::Network
    Qt5::Concurrent
)

# 编译测试可执行文件
add_executable(CompileTest
    compile_test.cpp
)

target_link_libraries(CompileTest
    Qt5::Core
)

# 多重继承测试可执行文件
add_executable(InheritanceTest
    inheritance_test.cpp
)

target_link_libraries(InheritanceTest
    CommunicationFramework
    Qt5::Core
    Qt5::Widgets
)

# 设置输出目录
set_target_properties(ExampleComplete PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(AlternativeImplementations PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(Qt5CompatibilityTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(CompileTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(InheritanceTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 编译选项
if(MSVC)
    target_compile_options(CommunicationFramework PRIVATE /W4)
    target_compile_options(ExampleComplete PRIVATE /W4)
    target_compile_options(AlternativeImplementations PRIVATE /W4)
    target_compile_options(Qt5CompatibilityTest PRIVATE /W4)
    target_compile_options(CompileTest PRIVATE /W4)
    target_compile_options(InheritanceTest PRIVATE /W4)
else()
    target_compile_options(CommunicationFramework PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(ExampleComplete PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(AlternativeImplementations PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(Qt5CompatibilityTest PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(CompileTest PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(InheritanceTest PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 安装规则
install(TARGETS ExampleComplete AlternativeImplementations Qt5CompatibilityTest CompileTest InheritanceTest
    RUNTIME DESTINATION bin
)

install(FILES
    communication_framework.h
    DESTINATION include
)

install(TARGETS CommunicationFramework
    ARCHIVE DESTINATION lib
)

# 打印配置信息
message(STATUS "Qt5 version: ${Qt5_VERSION}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
