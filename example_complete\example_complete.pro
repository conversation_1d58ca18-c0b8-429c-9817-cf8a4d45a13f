# 完整示例项目文件

TEMPLATE = app
CONFIG += c++17
TARGET = ExampleComplete

QT += core widgets network concurrent

# 源文件
SOURCES += \
    ../example_complete.cpp \
    ../communication_framework.cpp

# 头文件
HEADERS += \
    ../communication_framework.h

# 库依赖
LIBS += -L../lib -lCommunicationFramework

# 包含路径
INCLUDEPATH += ..

# 输出目录
DESTDIR = ../bin

# 编译选项
QMAKE_CXXFLAGS += -Wall -Wextra

# 目标平台特定设置
win32 {
    QMAKE_CXXFLAGS += /W4
    CONFIG += console
}

unix {
    QMAKE_CXXFLAGS += -Wpedantic
}

# 调试信息
CONFIG(debug, debug|release) {
    TARGET = $$join(TARGET,,,d)
    DEFINES += DEBUG_BUILD
}

# 版本信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "Qt5 Communication Framework"
QMAKE_TARGET_PRODUCT = "Complete Example Application"
QMAKE_TARGET_DESCRIPTION = "Complete example of multi-document view communication"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2025"
