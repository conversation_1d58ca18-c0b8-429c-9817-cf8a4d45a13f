# 通信框架库项目文件

TEMPLATE = lib
CONFIG += staticlib c++17
TARGET = CommunicationFramework

QT += core widgets network concurrent

# 源文件
SOURCES += \
    ../communication_framework.cpp

# 头文件
HEADERS += \
    ../communication_framework.h

# 输出目录
DESTDIR = ../lib

# 编译选项
QMAKE_CXXFLAGS += -Wall

# 目标平台特定设置
win32 {

    QMAKE_CFLAGS += /utf-8
    QMAKE_CXXFLAGS += /utf-8
}

unix {
    QMAKE_CXXFLAGS += -Wpedantic
}

# 调试信息
CONFIG(debug, debug|release) {
    TARGET = $$join(TARGET,,,d)
    DEFINES += DEBUG_BUILD
}

# 版本信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "Qt5 Communication Framework"
QMAKE_TARGET_PRODUCT = "Communication Framework Library"
QMAKE_TARGET_DESCRIPTION = "Multi-Document View Communication Framework for Qt5.14.2"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2025"
