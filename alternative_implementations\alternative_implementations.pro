# 替代实现示例项目文件

TEMPLATE = app
CONFIG += c++17
TARGET = AlternativeImplementations

QT += core widgets network concurrent

# 源文件
SOURCES += \
    ../alternative_implementations.cpp

# 输出目录
DESTDIR = ../bin

# 编译选项
QMAKE_CXXFLAGS += -Wall

# 目标平台特定设置
win32 {
    QMAKE_CXXFLAGS += /W4
    CONFIG += console
    QMAKE_CFLAGS += /utf-8
    QMAKE_CXXFLAGS += /utf-8
}

unix {
    QMAKE_CXXFLAGS += -Wpedantic
}

# 调试信息
CONFIG(debug, debug|release) {
    TARGET = $$join(TARGET,,,d)
    DEFINES += DEBUG_BUILD
}

# 版本信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "Qt5 Communication Framework"
QMAKE_TARGET_PRODUCT = "Alternative Implementations Demo"
QMAKE_TARGET_DESCRIPTION = "Demo of alternative communication implementations"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2025"
