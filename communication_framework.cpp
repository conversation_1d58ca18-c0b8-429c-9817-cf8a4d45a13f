#include "communication_framework.h"
#include <QDebug>
#include <QThread>
#include <QEventLoop>
#include <QTimer>

// 静态成员初始化
CommunicationBus* CommunicationBus::m_instance = nullptr;
QMutex CommunicationBus::m_mutex;

/**
 * @brief 获取通信总线单例实例
 */
CommunicationBus* CommunicationBus::instance() {
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new CommunicationBus();
        }
    }
    return m_instance;
}

/**
 * @brief 构造函数
 */
CommunicationBus::CommunicationBus(QObject* parent) : QObject(parent) {
    qDebug() << "CommunicationBus initialized";
}

/**
 * @brief 析构函数
 */
CommunicationBus::~CommunicationBus() {
    // 清理异步RPC监视器
    QMutexLocker asyncLocker(&m_asyncRpcMutex);
    for (auto watcher : m_asyncRpcWatchers) {
        watcher->deleteLater();
    }
    m_asyncRpcWatchers.clear();
}

/**
 * @brief 订阅消息主题
 */
void CommunicationBus::subscribe(const QString& topic, IMessageSubscriber* subscriber) {
    if (!subscriber) return;
    
    QMutexLocker locker(&m_subscribersMutex);
    if (!m_subscribers[topic].contains(subscriber)) {
        m_subscribers[topic].append(subscriber);
        qDebug() << "Subscriber added to topic:" << topic;
    }
}

/**
 * @brief 取消订阅消息主题
 */
void CommunicationBus::unsubscribe(const QString& topic, IMessageSubscriber* subscriber) {
    if (!subscriber) return;
    
    QMutexLocker locker(&m_subscribersMutex);
    m_subscribers[topic].removeAll(subscriber);
    if (m_subscribers[topic].isEmpty()) {
        m_subscribers.remove(topic);
    }
    qDebug() << "Subscriber removed from topic:" << topic;
}

/**
 * @brief 发布消息
 */
void CommunicationBus::publish(const Message& message) {
    QMutexLocker locker(&m_subscribersMutex);
    
    if (m_subscribers.contains(message.topic)) {
        for (auto subscriber : m_subscribers[message.topic]) {
            if (subscriber) {
                subscriber->onMessage(message);
            }
        }
    }
    
    emit messagePublished(message);
    qDebug() << "Message published to topic:" << message.topic;
}

/**
 * @brief 发布消息（便利方法）
 */
void CommunicationBus::publish(const QString& topic, const QJsonObject& data, const QString& sender) {
    Message message(topic, data, sender);
    publish(message);
}

/**
 * @brief 注册RPC服务
 */
void CommunicationBus::registerRpcService(const QString& serviceName, IRpcServiceProvider* provider) {
    if (!provider) return;
    
    QMutexLocker locker(&m_rpcServicesMutex);
    m_rpcServices[serviceName] = provider;
    qDebug() << "RPC service registered:" << serviceName;
}

/**
 * @brief 注销RPC服务
 */
void CommunicationBus::unregisterRpcService(const QString& serviceName) {
    QMutexLocker locker(&m_rpcServicesMutex);
    m_rpcServices.remove(serviceName);
    qDebug() << "RPC service unregistered:" << serviceName;
}

/**
 * @brief 同步RPC调用
 */
RpcResponse CommunicationBus::callRpcSync(const QString& serviceName, const QString& method,
                                         const QJsonObject& params, const QString& caller, int timeoutMs) {
    QMutexLocker locker(&m_rpcServicesMutex);
    
    if (!m_rpcServices.contains(serviceName)) {
        return RpcResponse("", false, QJsonObject(), "Service not found: " + serviceName);
    }
    
    IRpcServiceProvider* provider = m_rpcServices[serviceName];
    if (!provider) {
        return RpcResponse("", false, QJsonObject(), "Service provider is null");
    }
    
    RpcRequest request(method, params, caller);
    emit rpcCallReceived(request);
    
    // 使用QTimer实现超时机制
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot(true);
    
    RpcResponse response;
    bool completed = false;
    
    // 在新线程中执行RPC调用以避免阻塞
    auto future = QtConcurrent::run([provider, request]() {
        return provider->handleRpcCall(request);
    });
    
    QFutureWatcher<RpcResponse> watcher;
    connect(&watcher, &QFutureWatcher<RpcResponse>::finished, [&]() {
        response = watcher.result();
        completed = true;
        loop.quit();
    });
    
    connect(&timer, &QTimer::timeout, [&]() {
        completed = true;
        response = RpcResponse(request.id, false, QJsonObject(), "RPC call timeout");
        loop.quit();
    });
    
    watcher.setFuture(future);
    timer.start(timeoutMs);
    
    if (!completed) {
        loop.exec();
    }
    
    emit rpcResponseSent(response);
    qDebug() << "Sync RPC call completed:" << serviceName << "::" << method;
    
    return response;
}

/**
 * @brief 异步RPC调用
 */
QFuture<RpcResponse> CommunicationBus::callRpcAsync(const QString& serviceName, const QString& method,
                                                   const QJsonObject& params, const QString& caller) {
    QMutexLocker serviceLocker(&m_rpcServicesMutex);
    
    if (!m_rpcServices.contains(serviceName)) {
        QPromise<RpcResponse> promise;
        promise.start();
        promise.addResult(RpcResponse("", false, QJsonObject(), "Service not found: " + serviceName));
        promise.finish();
        return promise.future();
    }
    
    IRpcServiceProvider* provider = m_rpcServices[serviceName];
    if (!provider) {
        QPromise<RpcResponse> promise;
        promise.start();
        promise.addResult(RpcResponse("", false, QJsonObject(), "Service provider is null"));
        promise.finish();
        return promise.future();
    }
    
    RpcRequest request(method, params, caller);
    emit rpcCallReceived(request);
    
    // 创建异步任务
    auto future = QtConcurrent::run([provider, request, this]() {
        RpcResponse response = provider->handleRpcCall(request);
        emit rpcResponseSent(response);
        return response;
    });
    
    qDebug() << "Async RPC call initiated:" << serviceName << "::" << method;
    return future;
}

/**
 * @brief 处理异步RPC调用完成
 */
void CommunicationBus::handleAsyncRpcCall() {
    // 这个槽函数可以用于处理异步RPC调用的后续逻辑
    qDebug() << "Async RPC call handled";
}

// MessageSubscriberBase 实现

/**
 * @brief 消息订阅者基类构造函数
 */
MessageSubscriberBase::MessageSubscriberBase(QObject* parent) : QObject(parent) {
    // 连接信号槽
    connect(this, &MessageSubscriberBase::messageReceived, 
            this, &MessageSubscriberBase::handleMessage);
}

/**
 * @brief 消息订阅者基类析构函数
 */
MessageSubscriberBase::~MessageSubscriberBase() {
    // 取消所有订阅
    for (const QString& topic : m_subscribedTopics) {
        CommunicationBus::instance()->unsubscribe(topic, this);
    }
}

/**
 * @brief 订阅主题
 */
void MessageSubscriberBase::subscribeToTopic(const QString& topic) {
    if (!m_subscribedTopics.contains(topic)) {
        m_subscribedTopics.append(topic);
        CommunicationBus::instance()->subscribe(topic, this);
    }
}

/**
 * @brief 取消订阅主题
 */
void MessageSubscriberBase::unsubscribeFromTopic(const QString& topic) {
    if (m_subscribedTopics.contains(topic)) {
        m_subscribedTopics.removeAll(topic);
        CommunicationBus::instance()->unsubscribe(topic, this);
    }
}

/**
 * @brief 发布消息
 */
void MessageSubscriberBase::publishMessage(const QString& topic, const QJsonObject& data) {
    CommunicationBus::instance()->publish(topic, data, metaObject()->className());
}

/**
 * @brief 接收消息回调
 */
void MessageSubscriberBase::onMessage(const Message& message) {
    emit messageReceived(message.topic, message.data, message.sender);
}

// RpcServiceProviderBase 实现

/**
 * @brief RPC服务提供者基类构造函数
 */
RpcServiceProviderBase::RpcServiceProviderBase(const QString& serviceName, QObject* parent)
    : QObject(parent), m_serviceName(serviceName) {
    CommunicationBus::instance()->registerRpcService(serviceName, this);
}

/**
 * @brief RPC服务提供者基类析构函数
 */
RpcServiceProviderBase::~RpcServiceProviderBase() {
    CommunicationBus::instance()->unregisterRpcService(m_serviceName);
}

/**
 * @brief 处理RPC调用
 */
RpcResponse RpcServiceProviderBase::handleRpcCall(const RpcRequest& request) {
    try {
        QJsonObject result = processRpcMethod(request.method, request.params);
        return RpcResponse(request.id, true, result);
    } catch (const std::exception& e) {
        return RpcResponse(request.id, false, QJsonObject(), QString::fromStdString(e.what()));
    } catch (...) {
        return RpcResponse(request.id, false, QJsonObject(), "Unknown error occurred");
    }
}

#include "communication_framework.moc"
