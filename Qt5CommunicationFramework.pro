# Qt5.14.2 多文档View通信框架项目文件

TEMPLATE = subdirs

# 子项目
SUBDIRS += \
    framework \
    example_complete \
    alternative_implementations

# 依赖关系
example_complete.depends = framework
alternative_implementations.depends = framework

# 框架库子项目
framework.subdir = framework
framework.target = framework

# 完整示例子项目
example_complete.subdir = example_complete
example_complete.target = example_complete

# 替代实现示例子项目
alternative_implementations.subdir = alternative_implementations
alternative_implementations.target = alternative_implementations
