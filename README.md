# Qt5.14.2 多文档View通信框架

这是一个**严格基于Qt5.14.2和C++17**的多文档View通信框架，专为动态加载的插件机制设计，支持消息订阅和RPC调用两种通信方式。

> ⚠️ **重要说明**: 本框架专门针对Qt5.14.2进行了优化和兼容性处理，不使用任何Qt6特有的功能，确保在Qt5.14.2环境下完美运行。

## 功能特性

### 1. 消息订阅机制（类似MQTT）
- 基于Qt信号槽实现的发布/订阅模式
- 支持主题订阅和取消订阅
- 所有消息使用JSON格式传递
- 线程安全的消息分发
- 自动管理订阅者生命周期

### 2. RPC调用机制
- 支持同步和异步RPC调用
- JSON格式的参数和返回值
- 内置超时机制
- 错误处理和异常捕获
- 服务自动注册和注销

### 3. 框架特点
- 单例模式的通信总线
- 便利的基类简化使用
- 完全基于Qt5组件
- 易于扩展和维护
- 详细的日志和调试信息

## 项目结构

```
├── communication_framework.h      # 框架头文件
├── communication_framework.cpp    # 框架实现
├── example_complete.cpp          # 完整示例
├── alternative_implementations.cpp # 替代实现方案
├── CMakeLists.txt                # CMake构建文件
├── Qt5CommunicationFramework.pro # qmake项目文件
├── framework/                    # 框架库子项目
├── example_complete/            # 完整示例子项目
└── alternative_implementations/ # 替代实现子项目
```

## 核心组件

### CommunicationBus（通信总线）
- 单例模式的核心通信管理器
- 负责消息订阅/发布和RPC调用的统一管理
- 线程安全的实现

### MessageSubscriberBase（消息订阅者基类）
- 简化消息订阅的便利基类
- 自动管理订阅生命周期
- 基于Qt信号槽的消息处理

### RpcServiceProviderBase（RPC服务提供者基类）
- 简化RPC服务实现的基类
- 自动服务注册和错误处理
- 支持自定义方法处理

## 使用示例

### 消息订阅示例

```cpp
class MyView : public MessageSubscriberBase {
public:
    MyView() {
        subscribeToTopic("document.changed");
        subscribeToTopic("system.notification");
    }

protected:
    void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) override {
        if (topic == "document.changed") {
            QString docId = data["documentId"].toString();
            // 处理文档变更
        }
    }
};

// 发布消息
QJsonObject data;
data["documentId"] = "doc123";
data["content"] = "新内容";
publishMessage("document.changed", data);
```

### RPC调用示例

```cpp
class MyService : public RpcServiceProviderBase {
public:
    MyService() : RpcServiceProviderBase("MyService") {}

protected:
    QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) override {
        if (method == "getDocumentInfo") {
            QJsonObject result;
            result["title"] = "文档标题";
            result["wordCount"] = 1000;
            return result;
        }
        throw std::runtime_error("Unknown method");
    }
};

// 同步RPC调用
RpcResponse response = CommunicationBus::instance()->callRpcSync(
    "MyService", "getDocumentInfo", params, "caller", 5000);

// 异步RPC调用
auto future = CommunicationBus::instance()->callRpcAsync(
    "MyService", "getDocumentInfo", params, "caller");
```

## 构建方法

### 使用CMake构建

```bash
mkdir build
cd build
cmake ..
make
```

### 使用qmake构建

```bash
qmake Qt5CommunicationFramework.pro
make
```

## 运行示例

### 完整示例
展示两个模拟的文档编辑器View之间的通信：

```bash
./bin/ExampleComplete
```

### 替代实现示例
展示不同通信方式的对比：

```bash
./bin/AlternativeImplementations
```

### Qt5.14.2兼容性测试
验证框架在Qt5.14.2环境下的兼容性：

```bash
./bin/Qt5CompatibilityTest
```

## 替代实现方案

### 1. 共享内存通信（QSharedMemory）
**优点：**
- 性能极高，直接内存访问
- 适合大数据量传输
- 跨进程通信效率最高

**缺点：**
- 需要复杂的同步机制
- 内存管理复杂
- 不支持网络通信
- 调试困难

### 2. 本地套接字通信（QLocalSocket）
**优点：**
- 可靠的消息传递
- 支持双向通信
- 自动处理连接管理
- 调试相对容易

**缺点：**
- 性能比共享内存低
- 需要处理连接状态
- 仅限本地通信

### 3. 事件系统通信（QEvent）
**优点：**
- 完全基于Qt事件系统
- 类型安全
- 易于调试和扩展
- 支持事件过滤

**缺点：**
- 仅限同进程通信
- 事件队列可能成为瓶颈
- 需要自定义事件类型管理

## 扩展指南

### 添加新的消息类型
1. 定义新的主题名称
2. 创建对应的JSON数据结构
3. 在订阅者中处理新消息类型

### 添加新的RPC服务
1. 继承`RpcServiceProviderBase`
2. 重写`processRpcMethod`方法
3. 注册服务到通信总线

### 自定义通信协议
1. 实现`IMessageSubscriber`或`IRpcServiceProvider`接口
2. 注册到`CommunicationBus`
3. 处理自定义的消息格式

## 技术要求

- **Qt 5.14.2** (严格要求，不使用Qt6特性)
- **C++17** 支持的编译器 (GCC 7+, Clang 5+, MSVC 2017+)
- **CMake 3.16+** 或 **qmake**

## Qt5.14.2 兼容性

本框架经过专门的Qt5.14.2兼容性处理：

- ✅ 移除了Qt6的`QPromise`类依赖
- ✅ 使用Qt5兼容的`QtConcurrent::run()`实现异步操作
- ✅ 修正了多重继承的QObject转换问题
- ✅ 修正了结构体成员初始化语法
- ✅ 所有API都基于Qt5.14.2标准组件
- ✅ 通过完整的兼容性测试验证

详细的兼容性说明请参考 [QT5_COMPATIBILITY.md](QT5_COMPATIBILITY.md)

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
