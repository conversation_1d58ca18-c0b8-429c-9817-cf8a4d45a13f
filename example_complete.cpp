/**
 * @file example_complete.cpp
 * @brief 完整的Qt5.14.2多文档View通信框架示例
 * 
 * 本示例展示了：
 * 1. 消息订阅机制（类似MQTT）
 * 2. 同步和异步RPC调用
 * 3. 两个模拟的View插件之间的通信
 * 4. 所有通信都使用JSON格式
 */

#include "communication_framework.h"
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QLineEdit>
#include <QLabel>
#include <QSplitter>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTimer>
#include <QDebug>

/**
 * @brief 模拟的文档编辑器View插件
 */
class DocumentEditorView : public QWidget, public MessageSubscriberBase, public RpcServiceProviderBase {
    Q_OBJECT

public:
    explicit DocumentEditorView(const QString& viewName, QWidget* parent = nullptr)
        : QWidget(parent)
        , MessageSubscriberBase(this)
        , RpcServiceProviderBase("DocumentEditor_" + viewName, this)
        , m_viewName(viewName) {
        
        setupUI();
        setupCommunication();
        
        setWindowTitle("Document Editor - " + viewName);
        resize(400, 300);
    }

private slots:
    /**
     * @brief 处理接收到的消息
     */
    void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) override {
        QString message = QString("[%1] Received message on topic '%2' from '%3': %4")
                         .arg(m_viewName)
                         .arg(topic)
                         .arg(sender)
                         .arg(QJsonDocument(data).toJson(QJsonDocument::Compact));
        
        m_logTextEdit->append(message);
        
        // 处理特定的消息类型
        if (topic == "document.content.changed") {
            QString content = data["content"].toString();
            QString docId = data["documentId"].toString();
            m_logTextEdit->append(QString("[%1] Document %2 content changed: %3")
                                 .arg(m_viewName).arg(docId).arg(content));
        }
    }
    
    /**
     * @brief 发送消息按钮点击处理
     */
    void onSendMessageClicked() {
        QString topic = m_topicLineEdit->text().trimmed();
        QString content = m_messageLineEdit->text().trimmed();
        
        if (topic.isEmpty() || content.isEmpty()) {
            m_logTextEdit->append("[" + m_viewName + "] Topic and message cannot be empty!");
            return;
        }
        
        QJsonObject data;
        data["content"] = content;
        data["documentId"] = "doc_" + m_viewName.toLower();
        data["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        
        publishMessage(topic, data);
        m_logTextEdit->append(QString("[%1] Published message to topic '%2': %3")
                             .arg(m_viewName).arg(topic).arg(content));
        
        m_messageLineEdit->clear();
    }
    
    /**
     * @brief 同步RPC调用按钮点击处理
     */
    void onSyncRpcClicked() {
        QString serviceName = "DocumentEditor_" + (m_viewName == "View1" ? "View2" : "View1");
        QString method = "getDocumentInfo";
        
        QJsonObject params;
        params["documentId"] = "doc_" + m_viewName.toLower();
        params["requestedBy"] = m_viewName;
        
        m_logTextEdit->append(QString("[%1] Calling sync RPC: %2::%3")
                             .arg(m_viewName).arg(serviceName).arg(method));
        
        RpcResponse response = CommunicationBus::instance()->callRpcSync(
            serviceName, method, params, m_viewName, 3000);
        
        if (response.success) {
            m_logTextEdit->append(QString("[%1] Sync RPC success: %2")
                                 .arg(m_viewName)
                                 .arg(QJsonDocument(response.result).toJson(QJsonDocument::Compact)));
        } else {
            m_logTextEdit->append(QString("[%1] Sync RPC failed: %2")
                                 .arg(m_viewName).arg(response.error));
        }
    }
    
    /**
     * @brief 异步RPC调用按钮点击处理
     */
    void onAsyncRpcClicked() {
        QString serviceName = "DocumentEditor_" + (m_viewName == "View1" ? "View2" : "View1");
        QString method = "processDocument";
        
        QJsonObject params;
        params["documentId"] = "doc_" + m_viewName.toLower();
        params["operation"] = "format";
        params["requestedBy"] = m_viewName;
        
        m_logTextEdit->append(QString("[%1] Calling async RPC: %2::%3")
                             .arg(m_viewName).arg(serviceName).arg(method));
        
        auto future = CommunicationBus::instance()->callRpcAsync(
            serviceName, method, params, m_viewName);
        
        // 使用QFutureWatcher监听异步结果
        auto watcher = new QFutureWatcher<RpcResponse>(this);
        connect(watcher, &QFutureWatcher<RpcResponse>::finished, [this, watcher]() {
            RpcResponse response = watcher->result();
            if (response.success) {
                m_logTextEdit->append(QString("[%1] Async RPC success: %2")
                                     .arg(m_viewName)
                                     .arg(QJsonDocument(response.result).toJson(QJsonDocument::Compact)));
            } else {
                m_logTextEdit->append(QString("[%1] Async RPC failed: %2")
                                     .arg(m_viewName).arg(response.error));
            }
            watcher->deleteLater();
        });
        
        watcher->setFuture(future);
    }

protected:
    /**
     * @brief 处理RPC方法调用
     */
    QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) override {
        QJsonObject result;
        
        if (method == "getDocumentInfo") {
            // 模拟获取文档信息
            result["documentId"] = params["documentId"];
            result["title"] = "Document Title - " + m_viewName;
            result["wordCount"] = 1234;
            result["lastModified"] = QDateTime::currentDateTime().toString();
            result["owner"] = m_viewName;
            
            m_logTextEdit->append(QString("[%1] RPC method '%2' called with params: %3")
                                 .arg(m_viewName).arg(method)
                                 .arg(QJsonDocument(params).toJson(QJsonDocument::Compact)));
            
        } else if (method == "processDocument") {
            // 模拟文档处理
            QString operation = params["operation"].toString();
            result["documentId"] = params["documentId"];
            result["operation"] = operation;
            result["status"] = "completed";
            result["processedBy"] = m_viewName;
            result["processingTime"] = 150; // ms
            
            m_logTextEdit->append(QString("[%1] Processing document with operation: %2")
                                 .arg(m_viewName).arg(operation));
            
            // 模拟处理时间
            QThread::msleep(100);
            
        } else {
            throw std::runtime_error("Unknown RPC method: " + method.toStdString());
        }
        
        return result;
    }

private:
    void setupUI() {
        auto layout = new QVBoxLayout(this);
        
        // 日志显示区域
        m_logTextEdit = new QTextEdit();
        m_logTextEdit->setReadOnly(true);
        layout->addWidget(new QLabel("Communication Log:"));
        layout->addWidget(m_logTextEdit);
        
        // 消息发送区域
        auto messageLayout = new QHBoxLayout();
        messageLayout->addWidget(new QLabel("Topic:"));
        m_topicLineEdit = new QLineEdit("document.content.changed");
        messageLayout->addWidget(m_topicLineEdit);
        
        messageLayout->addWidget(new QLabel("Message:"));
        m_messageLineEdit = new QLineEdit("Hello from " + m_viewName);
        messageLayout->addWidget(m_messageLineEdit);
        
        m_sendMessageBtn = new QPushButton("Send Message");
        messageLayout->addWidget(m_sendMessageBtn);
        
        layout->addLayout(messageLayout);
        
        // RPC调用按钮
        auto rpcLayout = new QHBoxLayout();
        m_syncRpcBtn = new QPushButton("Sync RPC Call");
        m_asyncRpcBtn = new QPushButton("Async RPC Call");
        rpcLayout->addWidget(m_syncRpcBtn);
        rpcLayout->addWidget(m_asyncRpcBtn);
        layout->addLayout(rpcLayout);
        
        // 连接信号槽
        connect(m_sendMessageBtn, &QPushButton::clicked, this, &DocumentEditorView::onSendMessageClicked);
        connect(m_syncRpcBtn, &QPushButton::clicked, this, &DocumentEditorView::onSyncRpcClicked);
        connect(m_asyncRpcBtn, &QPushButton::clicked, this, &DocumentEditorView::onAsyncRpcClicked);
    }
    
    void setupCommunication() {
        // 订阅相关主题
        subscribeToTopic("document.content.changed");
        subscribeToTopic("document.saved");
        subscribeToTopic("system.notification");
        
        m_logTextEdit->append("[" + m_viewName + "] Communication setup completed");
        m_logTextEdit->append("[" + m_viewName + "] Subscribed to topics: document.content.changed, document.saved, system.notification");
        m_logTextEdit->append("[" + m_viewName + "] RPC service registered: DocumentEditor_" + m_viewName);
    }

private:
    QString m_viewName;
    QTextEdit* m_logTextEdit;
    QLineEdit* m_topicLineEdit;
    QLineEdit* m_messageLineEdit;
    QPushButton* m_sendMessageBtn;
    QPushButton* m_syncRpcBtn;
    QPushButton* m_asyncRpcBtn;
};

/**
 * @brief 主应用程序类
 */
class MainApplication : public QWidget {
    Q_OBJECT

public:
    explicit MainApplication(QWidget* parent = nullptr) : QWidget(parent) {
        setupUI();
        setWindowTitle("Qt5.14.2 Multi-Document View Communication Framework Demo");
        resize(1000, 600);
        
        // 启动定时器发送系统通知
        auto timer = new QTimer(this);
        connect(timer, &QTimer::timeout, this, &MainApplication::sendSystemNotification);
        timer->start(10000); // 每10秒发送一次系统通知
    }

private slots:
    void sendSystemNotification() {
        static int counter = 1;
        QJsonObject data;
        data["message"] = QString("System notification #%1").arg(counter++);
        data["level"] = "info";
        data["timestamp"] = QDateTime::currentDateTime().toString();
        
        CommunicationBus::instance()->publish("system.notification", data, "System");
    }

private:
    void setupUI() {
        auto layout = new QHBoxLayout(this);
        
        // 创建两个文档编辑器视图
        m_view1 = new DocumentEditorView("View1");
        m_view2 = new DocumentEditorView("View2");
        
        auto splitter = new QSplitter(Qt::Horizontal);
        splitter->addWidget(m_view1);
        splitter->addWidget(m_view2);
        splitter->setStretchFactor(0, 1);
        splitter->setStretchFactor(1, 1);
        
        layout->addWidget(splitter);
    }

private:
    DocumentEditorView* m_view1;
    DocumentEditorView* m_view2;
};

/**
 * @brief 主函数
 */
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << "Starting Qt5.14.2 Multi-Document View Communication Framework Demo";
    
    MainApplication mainApp;
    mainApp.show();
    
    return app.exec();
}

#include "example_complete.moc"
