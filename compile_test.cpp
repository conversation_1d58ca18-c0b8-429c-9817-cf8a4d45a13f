/**
 * @file compile_test.cpp
 * @brief 简单的编译测试，验证结构体初始化是否正确
 */

#include "communication_framework.h"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    std::cout << "Testing Qt5.14.2 compatible struct initialization..." << std::endl;

    try {
        // 测试Message结构体
        Message msg1;
        std::cout << "✓ Default Message created, timestamp: " << msg1.timestamp << std::endl;

        QJsonObject testData;
        testData["test"] = "value";
        Message msg2("test.topic", testData, "TestSender");
        std::cout << "✓ Parameterized Message created: " << msg2.topic.toStdString()
                  << " from " << msg2.sender.toStdString() << std::endl;

        // 测试RpcRequest结构体
        RpcRequest req1;
        std::cout << "✓ Default RpcRequest created, id: " << req1.id.toStdString() << std::endl;

        QJsonObject params;
        params["param1"] = "value1";
        RpcRequest req2("testMethod", params, "TestCaller");
        std::cout << "✓ Parameterized RpcRequest created: " << req2.method.toStdString()
                  << " by " << req2.caller.toStdString() << std::endl;

        // 测试RpcResponse结构体
        RpcResponse resp1;
        std::cout << "✓ Default RpcResponse created, success: " << (resp1.success ? "true" : "false") << std::endl;

        QJsonObject result;
        result["result"] = "success";
        RpcResponse resp2("req123", true, result, "");
        std::cout << "✓ Parameterized RpcResponse created: " << resp2.id.toStdString()
                  << " success: " << (resp2.success ? "true" : "false") << std::endl;

        std::cout << "\n🎉 All struct initialization tests passed!" << std::endl;
        std::cout << "✅ Qt5.14.2 compatibility confirmed." << std::endl;

    } catch (const std::exception& e) {
        std::cout << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }

    return 0;
}
