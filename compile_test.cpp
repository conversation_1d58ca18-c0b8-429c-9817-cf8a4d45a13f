/**
 * @file compile_test.cpp
 * @brief 简单的编译测试，验证结构体初始化是否正确
 */

#include "communication_framework.h"
#include <QApplication>
#include <QDebug>

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << "Testing Qt5.14.2 compatible struct initialization...";
    
    // 测试Message结构体
    Message msg1;
    qDebug() << "Default Message created, timestamp:" << msg1.timestamp;
    
    QJsonObject testData;
    testData["test"] = "value";
    Message msg2("test.topic", testData, "TestSender");
    qDebug() << "Parameterized Message created:" << msg2.topic << msg2.sender;
    
    // 测试RpcRequest结构体
    RpcRequest req1;
    qDebug() << "Default RpcRequest created, id:" << req1.id;
    
    QJsonObject params;
    params["param1"] = "value1";
    RpcRequest req2("testMethod", params, "TestCaller");
    qDebug() << "Parameterized RpcRequest created:" << req2.method << req2.caller;
    
    // 测试RpcResponse结构体
    RpcResponse resp1;
    qDebug() << "Default RpcResponse created, success:" << resp1.success;
    
    QJsonObject result;
    result["result"] = "success";
    RpcResponse resp2("req123", true, result, "");
    qDebug() << "Parameterized RpcResponse created:" << resp2.id << resp2.success;
    
    qDebug() << "All struct initialization tests passed!";
    qDebug() << "Qt5.14.2 compatibility confirmed.";
    
    return 0;
}
