/**
 * @file inheritance_test.cpp
 * @brief 测试多重继承的QObject转换问题修正
 */

#include "communication_framework.h"
#include <QCoreApplication>
#include <QWidget>
#include <iostream>

/**
 * @brief 测试多重继承类
 */
class TestMultipleInheritance : public QWidget, public MessageSubscriberBase, public RpcServiceProviderBase {
    Q_OBJECT

public:
    explicit TestMultipleInheritance(QWidget* parent = nullptr)
        : QWidget(parent)
        , MessageSubscriberBase(static_cast<QObject*>(this))
        , RpcServiceProviderBase("TestService", static_cast<QObject*>(this)) {
        
        std::cout << "✓ Multiple inheritance class created successfully" << std::endl;
        std::cout << "  - QWidget parent: " << (QWidget::parent() ? "set" : "null") << std::endl;
        std::cout << "  - MessageSubscriberBase initialized" << std::endl;
        std::cout << "  - RpcServiceProviderBase initialized with service: TestService" << std::endl;
    }

protected:
    void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) override {
        std::cout << "Message received - Topic: " << topic.toStdString() 
                  << ", Sender: " << sender.toStdString() << std::endl;
    }
    
    QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) override {
        std::cout << "RPC method called: " << method.toStdString() << std::endl;
        
        QJsonObject result;
        result["status"] = "success";
        result["method"] = method;
        result["processed_by"] = "TestMultipleInheritance";
        
        return result;
    }
};

/**
 * @brief 另一个测试类，验证不同的继承顺序
 */
class TestDifferentOrder : public MessageSubscriberBase, public QWidget, public RpcServiceProviderBase {
    Q_OBJECT

public:
    explicit TestDifferentOrder(QWidget* parent = nullptr)
        : MessageSubscriberBase(static_cast<QObject*>(this))
        , QWidget(parent)
        , RpcServiceProviderBase("TestService2", static_cast<QObject*>(this)) {
        
        std::cout << "✓ Different inheritance order class created successfully" << std::endl;
    }

protected:
    void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) override {
        std::cout << "Message received in different order class" << std::endl;
    }
    
    QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) override {
        QJsonObject result;
        result["status"] = "success";
        result["class"] = "TestDifferentOrder";
        return result;
    }
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Testing Qt5.14.2 multiple inheritance compatibility..." << std::endl;
    std::cout << "=================================================" << std::endl;
    
    try {
        // 测试第一种继承顺序
        std::cout << "\n1. Testing QWidget, MessageSubscriberBase, RpcServiceProviderBase order:" << std::endl;
        TestMultipleInheritance test1;
        
        // 测试消息订阅功能
        test1.subscribeToTopic("test.topic");
        
        QJsonObject testData;
        testData["message"] = "Hello from inheritance test";
        test1.publishMessage("test.topic", testData);
        
        // 测试RPC功能
        RpcResponse response = CommunicationBus::instance()->callRpcSync(
            "TestService", "testMethod", QJsonObject(), "InheritanceTest", 1000);
        
        if (response.success) {
            std::cout << "✓ RPC call successful" << std::endl;
        } else {
            std::cout << "✗ RPC call failed: " << response.error.toStdString() << std::endl;
        }
        
        // 测试第二种继承顺序
        std::cout << "\n2. Testing different inheritance order:" << std::endl;
        TestDifferentOrder test2;
        
        test2.subscribeToTopic("test.topic2");
        test2.publishMessage("test.topic2", testData);
        
        std::cout << "\n🎉 All multiple inheritance tests passed!" << std::endl;
        std::cout << "✅ Qt5.14.2 multiple inheritance compatibility confirmed." << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}

#include "inheritance_test.moc"
