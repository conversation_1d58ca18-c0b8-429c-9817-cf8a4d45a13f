@echo off
echo Qt5.14.2 Communication Framework Build Test
echo ==========================================

REM 检查Qt环境
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: qmake not found in PATH. Please ensure Qt5.14.2 is properly installed.
    pause
    exit /b 1
)

echo Qt version:
qmake -version

REM 创建构建目录
if not exist build mkdir build
cd build

echo.
echo Building with qmake...
echo ----------------------

REM 使用qmake构建
qmake ../Qt5CommunicationFramework.pro
if %errorlevel% neq 0 (
    echo Error: qmake failed
    pause
    exit /b 1
)

REM 编译项目
nmake
if %errorlevel% neq 0 (
    echo Error: nmake failed, trying mingw32-make...
    mingw32-make
    if %errorlevel% neq 0 (
        echo Error: Build failed
        pause
        exit /b 1
    )
)

echo.
echo Build completed successfully!
echo Generated files:
dir /b bin\*.exe 2>nul
dir /b lib\*.lib 2>nul
dir /b lib\*.a 2>nul

echo.
echo To run the examples:
echo   bin\ExampleComplete.exe
echo   bin\AlternativeImplementations.exe
echo   bin\Qt5CompatibilityTest.exe
echo   bin\CompileTest.exe
echo   bin\InheritanceTest.exe

echo.
echo Running compile test to verify Qt5.14.2 compatibility...
bin\CompileTest.exe

echo.
echo Running inheritance test to verify multiple inheritance fixes...
bin\InheritanceTest.exe

pause
