#ifndef COMMUNICATION_FRAMEWORK_H
#define COMMUNICATION_FRAMEWORK_H

#include <QObject>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonValue>
#include <QVariant>
#include <QMap>
#include <QList>
#include <QMutex>
#include <QTimer>
#include <QUuid>
#include <QDateTime>
#include <QFuture>
#include <QFutureWatcher>
#include <QtConcurrent>
#include <functional>
#include <memory>
#include <QtWidgets>

/**
 * @brief 消息订阅机制的消息结构
 */
struct Message {
    QString topic;          // 消息主题
    QJsonObject data;       // 消息数据(JSON格式)
    QString sender;         // 发送者标识
    qint64 timestamp;       // 时间戳

    Message() {
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }

    Message(const QString& t, const QJsonObject& d, const QString& s = QString()) {
        topic = t;
        data = d;
        sender = s;
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }
};

/**
 * @brief RPC调用请求结构
 */
struct RpcRequest {
    QString id;             // 请求唯一标识
    QString method;         // 方法名
    QJsonObject params;     // 参数(JSON格式)
    QString caller;         // 调用者标识
    qint64 timestamp;       // 时间戳

    RpcRequest() {
        id = QUuid::createUuid().toString();
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }

    RpcRequest(const QString& m, const QJsonObject& p, const QString& c = QString()) {
        id = QUuid::createUuid().toString();
        method = m;
        params = p;
        caller = c;
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }
};

/**
 * @brief RPC调用响应结构
 */
struct RpcResponse {
    QString id;             // 对应请求的ID
    bool success;           // 是否成功
    QJsonObject result;     // 结果数据(JSON格式)
    QString error;          // 错误信息
    qint64 timestamp;       // 时间戳

    RpcResponse() {
        success = false;
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }

    RpcResponse(const QString& reqId, bool succ, const QJsonObject& res, const QString& err = QString()) {
        id = reqId;
        success = succ;
        result = res;
        error = err;
        timestamp = QDateTime::currentMSecsSinceEpoch();
    }
};

/**
 * @brief 消息订阅者接口
 */
class IMessageSubscriber {
public:
    virtual ~IMessageSubscriber() = default;
    virtual void onMessage(const Message& message) = 0;
};

/**
 * @brief RPC服务提供者接口
 */
class IRpcServiceProvider {
public:
    virtual ~IRpcServiceProvider() = default;
    virtual RpcResponse handleRpcCall(const RpcRequest& request) = 0;
};

/**
 * @brief 通信总线 - 核心通信管理器
 * 负责消息订阅/发布和RPC调用的统一管理
 */
class CommunicationBus : public QObject {
    Q_OBJECT

public:
    static CommunicationBus* instance();
    
    // 消息订阅机制
    void subscribe(const QString& topic, IMessageSubscriber* subscriber);
    void unsubscribe(const QString& topic, IMessageSubscriber* subscriber);
    void publish(const Message& message);
    void publish(const QString& topic, const QJsonObject& data, const QString& sender = QString());
    
    // RPC调用机制
    void registerRpcService(const QString& serviceName, IRpcServiceProvider* provider);
    void unregisterRpcService(const QString& serviceName);
    
    // 同步RPC调用
    RpcResponse callRpcSync(const QString& serviceName, const QString& method, 
                           const QJsonObject& params, const QString& caller = QString(), 
                           int timeoutMs = 5000);
    
    // 异步RPC调用
    QFuture<RpcResponse> callRpcAsync(const QString& serviceName, const QString& method,
                                     const QJsonObject& params, const QString& caller = QString());

signals:
    void messagePublished(const Message& message);
    void rpcCallReceived(const RpcRequest& request);
    void rpcResponseSent(const RpcResponse& response);

private slots:
    void handleAsyncRpcCall();

private:
    CommunicationBus(QObject* parent = nullptr);
    ~CommunicationBus();
    
    static CommunicationBus* m_instance;
    static QMutex m_mutex;
    
    // 消息订阅相关
    QMap<QString, QList<IMessageSubscriber*>> m_subscribers;
    QMutex m_subscribersMutex;
    
    // RPC服务相关
    QMap<QString, IRpcServiceProvider*> m_rpcServices;
    QMutex m_rpcServicesMutex;
    
    // 异步RPC调用相关
    QMap<QString, QFutureWatcher<RpcResponse>*> m_asyncRpcWatchers;
    QMutex m_asyncRpcMutex;
};

/**
 * @brief 便利的消息订阅者基类
 * 使用Qt信号槽机制简化消息处理
 */
class MessageSubscriberBase : public QObject, public IMessageSubscriber {
    Q_OBJECT

public:
    explicit MessageSubscriberBase(QObject* parent = nullptr);
    virtual ~MessageSubscriberBase();
    
    void subscribeToTopic(const QString& topic);
    void unsubscribeFromTopic(const QString& topic);
    void publishMessage(const QString& topic, const QJsonObject& data);

protected:
    void onMessage(const Message& message) override;

signals:
    void messageReceived(const QString& topic, const QJsonObject& data, const QString& sender);

protected slots:
    virtual void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) = 0;

private:
    QList<QString> m_subscribedTopics;
};

/**
 * @brief 便利的RPC服务提供者基类
 */
class RpcServiceProviderBase : public QObject, public IRpcServiceProvider {
    Q_OBJECT

public:
    explicit RpcServiceProviderBase(const QString& serviceName, QObject* parent = nullptr);
    virtual ~RpcServiceProviderBase();

protected:
    RpcResponse handleRpcCall(const RpcRequest& request) override;
    
    // 子类需要重写此方法来处理具体的RPC调用
    virtual QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) = 0;
    
    QString m_serviceName;
};

#endif // COMMUNICATION_FRAMEWORK_H
