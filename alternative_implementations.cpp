/**
 * @file alternative_implementations.cpp
 * @brief Qt5.14.2多文档View通信的替代实现方案
 * 
 * 本文件展示了几种不同的通信实现方式及其优缺点：
 * 1. 基于QSharedMemory的共享内存通信
 * 2. 基于QLocalSocket的本地套接字通信
 * 3. 基于QDBus的D-Bus通信（Linux）
 * 4. 基于自定义事件系统的通信
 */

#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QTextEdit>
#include <QPushButton>
#include <QJsonObject>
#include <QJsonDocument>
#include <QSharedMemory>
#include <QLocalSocket>
#include <QLocalServer>
#include <QTimer>
#include <QDebug>
#include <QEvent>
#include <QMutex>
#include <QBuffer>
#include <QDataStream>

/**
 * @brief 方案1: 基于QSharedMemory的共享内存通信
 * 
 * 优点:
 * - 性能极高，直接内存访问
 * - 适合大数据量传输
 * - 跨进程通信效率最高
 * 
 * 缺点:
 * - 需要复杂的同步机制
 * - 内存管理复杂
 * - 不支持网络通信
 * - 调试困难
 */
class SharedMemoryCommunication : public QObject {
    Q_OBJECT

public:
    explicit SharedMemoryCommunication(const QString& key, QObject* parent = nullptr)
        : QObject(parent), m_key(key) {
        
        m_sharedMemory = new QSharedMemory(key, this);
        
        // 尝试创建共享内存
        if (!m_sharedMemory->create(1024 * 1024)) { // 1MB
            if (m_sharedMemory->error() == QSharedMemory::AlreadyExists) {
                m_sharedMemory->attach();
                qDebug() << "Attached to existing shared memory:" << key;
            } else {
                qDebug() << "Failed to create shared memory:" << m_sharedMemory->errorString();
            }
        } else {
            qDebug() << "Created shared memory:" << key;
        }
        
        // 启动定时器检查新消息
        m_timer = new QTimer(this);
        connect(m_timer, &QTimer::timeout, this, &SharedMemoryCommunication::checkForMessages);
        m_timer->start(100); // 每100ms检查一次
    }
    
    void sendMessage(const QJsonObject& message) {
        if (!m_sharedMemory->isAttached()) return;
        
        QJsonDocument doc(message);
        QByteArray data = doc.toJson(QJsonDocument::Compact);
        
        m_sharedMemory->lock();
        
        // 写入消息长度和数据
        char* memory = static_cast<char*>(m_sharedMemory->data());
        qint32 size = data.size();
        memcpy(memory, &size, sizeof(qint32));
        memcpy(memory + sizeof(qint32), data.constData(), size);
        
        m_sharedMemory->unlock();
        
        qDebug() << "Sent message via shared memory:" << data;
    }

signals:
    void messageReceived(const QJsonObject& message);

private slots:
    void checkForMessages() {
        if (!m_sharedMemory->isAttached()) return;
        
        m_sharedMemory->lock();
        
        char* memory = static_cast<char*>(m_sharedMemory->data());
        qint32 size;
        memcpy(&size, memory, sizeof(qint32));
        
        if (size > 0 && size < 1024 * 1024) {
            QByteArray data(memory + sizeof(qint32), size);
            QJsonDocument doc = QJsonDocument::fromJson(data);
            
            if (!doc.isNull()) {
                emit messageReceived(doc.object());
                
                // 清除消息
                memset(memory, 0, sizeof(qint32));
            }
        }
        
        m_sharedMemory->unlock();
    }

private:
    QString m_key;
    QSharedMemory* m_sharedMemory;
    QTimer* m_timer;
};

/**
 * @brief 方案2: 基于QLocalSocket的本地套接字通信
 * 
 * 优点:
 * - 可靠的消息传递
 * - 支持双向通信
 * - 自动处理连接管理
 * - 调试相对容易
 * 
 * 缺点:
 * - 性能比共享内存低
 * - 需要处理连接状态
 * - 仅限本地通信
 */
class LocalSocketCommunication : public QObject {
    Q_OBJECT

public:
    explicit LocalSocketCommunication(const QString& serverName, bool isServer = false, QObject* parent = nullptr)
        : QObject(parent), m_serverName(serverName), m_isServer(isServer) {
        
        if (m_isServer) {
            setupServer();
        } else {
            setupClient();
        }
    }

    void sendMessage(const QJsonObject& message) {
        QJsonDocument doc(message);
        QByteArray data = doc.toJson(QJsonDocument::Compact) + "\n";
        
        if (m_isServer && !m_clientSockets.isEmpty()) {
            for (QLocalSocket* socket : m_clientSockets) {
                if (socket->state() == QLocalSocket::ConnectedState) {
                    socket->write(data);
                    socket->flush();
                }
            }
        } else if (!m_isServer && m_socket && m_socket->state() == QLocalSocket::ConnectedState) {
            m_socket->write(data);
            m_socket->flush();
        }
        
        qDebug() << "Sent message via local socket:" << data.trimmed();
    }

signals:
    void messageReceived(const QJsonObject& message);
    void clientConnected();
    void clientDisconnected();

private slots:
    void onNewConnection() {
        if (!m_server) return;
        
        QLocalSocket* clientSocket = m_server->nextPendingConnection();
        m_clientSockets.append(clientSocket);
        
        connect(clientSocket, &QLocalSocket::readyRead, [this, clientSocket]() {
            QByteArray data = clientSocket->readAll();
            processReceivedData(data);
        });
        
        connect(clientSocket, &QLocalSocket::disconnected, [this, clientSocket]() {
            m_clientSockets.removeAll(clientSocket);
            clientSocket->deleteLater();
            emit clientDisconnected();
        });
        
        emit clientConnected();
        qDebug() << "Client connected to local socket server";
    }
    
    void onSocketReadyRead() {
        if (!m_socket) return;
        
        QByteArray data = m_socket->readAll();
        processReceivedData(data);
    }

private:
    void setupServer() {
        m_server = new QLocalServer(this);
        
        // 移除可能存在的旧服务器
        QLocalServer::removeServer(m_serverName);
        
        if (m_server->listen(m_serverName)) {
            connect(m_server, &QLocalServer::newConnection, this, &LocalSocketCommunication::onNewConnection);
            qDebug() << "Local socket server started:" << m_serverName;
        } else {
            qDebug() << "Failed to start local socket server:" << m_server->errorString();
        }
    }
    
    void setupClient() {
        m_socket = new QLocalSocket(this);
        connect(m_socket, &QLocalSocket::readyRead, this, &LocalSocketCommunication::onSocketReadyRead);
        connect(m_socket, &QLocalSocket::connected, []() {
            qDebug() << "Connected to local socket server";
        });
        
        m_socket->connectToServer(m_serverName);
    }
    
    void processReceivedData(const QByteArray& data) {
        m_buffer.append(data);
        
        // 处理完整的JSON消息（以换行符分隔）
        while (m_buffer.contains('\n')) {
            int index = m_buffer.indexOf('\n');
            QByteArray messageData = m_buffer.left(index);
            m_buffer.remove(0, index + 1);
            
            QJsonDocument doc = QJsonDocument::fromJson(messageData);
            if (!doc.isNull()) {
                emit messageReceived(doc.object());
            }
        }
    }

private:
    QString m_serverName;
    bool m_isServer;
    QLocalServer* m_server = nullptr;
    QLocalSocket* m_socket = nullptr;
    QList<QLocalSocket*> m_clientSockets;
    QByteArray m_buffer;
};

/**
 * @brief 方案3: 基于自定义事件系统的通信
 * 
 * 优点:
 * - 完全基于Qt事件系统
 * - 类型安全
 * - 易于调试和扩展
 * - 支持事件过滤
 * 
 * 缺点:
 * - 仅限同进程通信
 * - 事件队列可能成为瓶颈
 * - 需要自定义事件类型管理
 */

// 自定义事件类型
const QEvent::Type CustomMessageEvent = static_cast<QEvent::Type>(QEvent::User + 1);
const QEvent::Type CustomRpcEvent = static_cast<QEvent::Type>(QEvent::User + 2);

class MessageEvent : public QEvent {
public:
    MessageEvent(const QString& topic, const QJsonObject& data, const QString& sender = QString())
        : QEvent(CustomMessageEvent), m_topic(topic), m_data(data), m_sender(sender) {}
    
    QString topic() const { return m_topic; }
    QJsonObject data() const { return m_data; }
    QString sender() const { return m_sender; }

private:
    QString m_topic;
    QJsonObject m_data;
    QString m_sender;
};

class RpcEvent : public QEvent {
public:
    RpcEvent(const QString& method, const QJsonObject& params, const QString& requestId)
        : QEvent(CustomRpcEvent), m_method(method), m_params(params), m_requestId(requestId) {}
    
    QString method() const { return m_method; }
    QJsonObject params() const { return m_params; }
    QString requestId() const { return m_requestId; }

private:
    QString m_method;
    QJsonObject m_params;
    QString m_requestId;
};

class EventBasedCommunication : public QObject {
    Q_OBJECT

public:
    static EventBasedCommunication* instance() {
        static EventBasedCommunication* inst = new EventBasedCommunication();
        return inst;
    }
    
    void registerReceiver(QObject* receiver) {
        if (!m_receivers.contains(receiver)) {
            m_receivers.append(receiver);
            qDebug() << "Registered event receiver:" << receiver->objectName();
        }
    }
    
    void unregisterReceiver(QObject* receiver) {
        m_receivers.removeAll(receiver);
        qDebug() << "Unregistered event receiver:" << receiver->objectName();
    }
    
    void sendMessage(const QString& topic, const QJsonObject& data, const QString& sender = QString()) {
        for (QObject* receiver : m_receivers) {
            QCoreApplication::postEvent(receiver, new MessageEvent(topic, data, sender));
        }
        qDebug() << "Sent message event to" << m_receivers.size() << "receivers";
    }
    
    void sendRpcCall(const QString& method, const QJsonObject& params, const QString& requestId) {
        for (QObject* receiver : m_receivers) {
            QCoreApplication::postEvent(receiver, new RpcEvent(method, params, requestId));
        }
        qDebug() << "Sent RPC event to" << m_receivers.size() << "receivers";
    }

private:
    EventBasedCommunication() = default;
    QList<QObject*> m_receivers;
};

/**
 * @brief 演示各种通信方式的测试窗口
 */
class AlternativeImplementationDemo : public QWidget {
    Q_OBJECT

public:
    explicit AlternativeImplementationDemo(QWidget* parent = nullptr) : QWidget(parent) {
        setupUI();
        setupCommunications();
        setWindowTitle("Alternative Communication Implementations Demo");
        resize(800, 600);
    }

protected:
    bool event(QEvent* event) override {
        if (event->type() == CustomMessageEvent) {
            MessageEvent* msgEvent = static_cast<MessageEvent*>(event);
            m_logTextEdit->append(QString("Event System - Message: Topic=%1, Data=%2, Sender=%3")
                                 .arg(msgEvent->topic())
                                 .arg(QJsonDocument(msgEvent->data()).toJson(QJsonDocument::Compact))
                                 .arg(msgEvent->sender()));
            return true;
        } else if (event->type() == CustomRpcEvent) {
            RpcEvent* rpcEvent = static_cast<RpcEvent*>(event);
            m_logTextEdit->append(QString("Event System - RPC: Method=%1, Params=%2, RequestId=%3")
                                 .arg(rpcEvent->method())
                                 .arg(QJsonDocument(rpcEvent->params()).toJson(QJsonDocument::Compact))
                                 .arg(rpcEvent->requestId()));
            return true;
        }
        
        return QWidget::event(event);
    }

private slots:
    void testSharedMemory() {
        QJsonObject message;
        message["type"] = "test";
        message["content"] = "Hello from shared memory!";
        message["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        
        m_sharedMemComm->sendMessage(message);
    }
    
    void testLocalSocket() {
        QJsonObject message;
        message["type"] = "test";
        message["content"] = "Hello from local socket!";
        message["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        
        m_localSocketComm->sendMessage(message);
    }
    
    void testEventSystem() {
        QJsonObject data;
        data["content"] = "Hello from event system!";
        data["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        
        EventBasedCommunication::instance()->sendMessage("test.topic", data, "Demo");
        
        QJsonObject rpcParams;
        rpcParams["param1"] = "value1";
        EventBasedCommunication::instance()->sendRpcCall("testMethod", rpcParams, "req_123");
    }

private:
    void setupUI() {
        auto layout = new QVBoxLayout(this);
        
        m_logTextEdit = new QTextEdit();
        m_logTextEdit->setReadOnly(true);
        layout->addWidget(m_logTextEdit);
        
        auto buttonLayout = new QHBoxLayout();
        
        auto sharedMemBtn = new QPushButton("Test Shared Memory");
        auto localSocketBtn = new QPushButton("Test Local Socket");
        auto eventSystemBtn = new QPushButton("Test Event System");
        
        buttonLayout->addWidget(sharedMemBtn);
        buttonLayout->addWidget(localSocketBtn);
        buttonLayout->addWidget(eventSystemBtn);
        
        layout->addLayout(buttonLayout);
        
        connect(sharedMemBtn, &QPushButton::clicked, this, &AlternativeImplementationDemo::testSharedMemory);
        connect(localSocketBtn, &QPushButton::clicked, this, &AlternativeImplementationDemo::testLocalSocket);
        connect(eventSystemBtn, &QPushButton::clicked, this, &AlternativeImplementationDemo::testEventSystem);
    }
    
    void setupCommunications() {
        // 共享内存通信
        m_sharedMemComm = new SharedMemoryCommunication("demo_shared_mem", this);
        connect(m_sharedMemComm, &SharedMemoryCommunication::messageReceived, [this](const QJsonObject& msg) {
            m_logTextEdit->append("Shared Memory - Received: " + 
                                 QJsonDocument(msg).toJson(QJsonDocument::Compact));
        });
        
        // 本地套接字通信
        m_localSocketComm = new LocalSocketCommunication("demo_local_socket", true, this);
        connect(m_localSocketComm, &LocalSocketCommunication::messageReceived, [this](const QJsonObject& msg) {
            m_logTextEdit->append("Local Socket - Received: " + 
                                 QJsonDocument(msg).toJson(QJsonDocument::Compact));
        });
        
        // 事件系统通信
        EventBasedCommunication::instance()->registerReceiver(this);
        
        m_logTextEdit->append("=== Alternative Communication Implementations Demo ===");
        m_logTextEdit->append("1. Shared Memory: High performance, complex synchronization");
        m_logTextEdit->append("2. Local Socket: Reliable, moderate performance");
        m_logTextEdit->append("3. Event System: Qt-native, same-process only");
        m_logTextEdit->append("Click buttons to test each implementation.");
    }

private:
    QTextEdit* m_logTextEdit;
    SharedMemoryCommunication* m_sharedMemComm;
    LocalSocketCommunication* m_localSocketComm;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    AlternativeImplementationDemo demo;
    demo.show();
    
    return app.exec();
}

#include "alternative_implementations.moc"
