/**
 * @file qt5_compatibility_test.cpp
 * @brief Qt5.14.2兼容性测试
 * 
 * 本文件用于测试通信框架在Qt5.14.2环境下的兼容性
 * 验证所有API都能正常工作，不使用Qt6特有的功能
 */

#include "communication_framework.h"
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFutureWatcher>

/**
 * @brief Qt5.14.2兼容性测试类
 */
class Qt5CompatibilityTest : public QWidget, public MessageSubscriberBase, public RpcServiceProviderBase {
    Q_OBJECT

public:
    explicit Qt5CompatibilityTest(QWidget* parent = nullptr)
        : QWidget(parent)
        , MessageSubscriberBase(static_cast<QObject*>(this))
        , RpcServiceProviderBase("Qt5TestService", static_cast<QObject*>(this)) {
        
        setupUI();
        setupTests();
        setWindowTitle("Qt5.14.2 Compatibility Test");
        resize(600, 400);
        
        // 开始自动测试
        QTimer::singleShot(1000, this, &Qt5CompatibilityTest::runAutomaticTests);
    }

private slots:
    /**
     * @brief 处理接收到的消息
     */
    void handleMessage(const QString& topic, const QJsonObject& data, const QString& sender) override {
        QString message = QString("[Qt5 Test] Received message on topic '%1' from '%2': %3")
                         .arg(topic)
                         .arg(sender)
                         .arg(QJsonDocument(data).toJson(QJsonDocument::Compact));
        
        m_logTextEdit->append(message);
        m_testResults.append("✓ Message subscription works");
    }
    
    /**
     * @brief 运行自动测试
     */
    void runAutomaticTests() {
        m_logTextEdit->append("=== Starting Qt5.14.2 Compatibility Tests ===");
        
        // 测试1: 消息订阅和发布
        testMessagePubSub();
        
        // 测试2: 同步RPC调用
        QTimer::singleShot(500, this, &Qt5CompatibilityTest::testSyncRpc);
        
        // 测试3: 异步RPC调用
        QTimer::singleShot(1000, this, &Qt5CompatibilityTest::testAsyncRpc);
        
        // 测试4: 错误处理
        QTimer::singleShot(1500, this, &Qt5CompatibilityTest::testErrorHandling);
        
        // 显示测试结果
        QTimer::singleShot(2500, this, &Qt5CompatibilityTest::showTestResults);
    }
    
    /**
     * @brief 测试消息发布订阅
     */
    void testMessagePubSub() {
        m_logTextEdit->append("\n--- Test 1: Message Pub/Sub ---");
        
        try {
            QJsonObject testData;
            testData["testId"] = "msg_test_001";
            testData["content"] = "Qt5.14.2 compatibility test message";
            testData["timestamp"] = QDateTime::currentMSecsSinceEpoch();
            
            publishMessage("qt5.test.message", testData);
            m_testResults.append("✓ Message publishing works");
            
        } catch (const std::exception& e) {
            m_logTextEdit->append("✗ Message test failed: " + QString::fromStdString(e.what()));
            m_testResults.append("✗ Message publishing failed");
        }
    }
    
    /**
     * @brief 测试同步RPC调用
     */
    void testSyncRpc() {
        m_logTextEdit->append("\n--- Test 2: Synchronous RPC ---");
        
        try {
            QJsonObject params;
            params["testParam"] = "sync_test_value";
            params["requestId"] = "sync_001";
            
            RpcResponse response = CommunicationBus::instance()->callRpcSync(
                "Qt5TestService", "testMethod", params, "Qt5Test", 3000);
            
            if (response.success) {
                m_logTextEdit->append("✓ Sync RPC call successful: " + 
                                     QJsonDocument(response.result).toJson(QJsonDocument::Compact));
                m_testResults.append("✓ Synchronous RPC works");
            } else {
                m_logTextEdit->append("✗ Sync RPC call failed: " + response.error);
                m_testResults.append("✗ Synchronous RPC failed");
            }
            
        } catch (const std::exception& e) {
            m_logTextEdit->append("✗ Sync RPC test failed: " + QString::fromStdString(e.what()));
            m_testResults.append("✗ Synchronous RPC failed");
        }
    }
    
    /**
     * @brief 测试异步RPC调用
     */
    void testAsyncRpc() {
        m_logTextEdit->append("\n--- Test 3: Asynchronous RPC ---");
        
        try {
            QJsonObject params;
            params["testParam"] = "async_test_value";
            params["requestId"] = "async_001";
            
            auto future = CommunicationBus::instance()->callRpcAsync(
                "Qt5TestService", "asyncTestMethod", params, "Qt5Test");
            
            // 使用QFutureWatcher监听结果（Qt5.14.2兼容方式）
            auto watcher = new QFutureWatcher<RpcResponse>(this);
            connect(watcher, &QFutureWatcher<RpcResponse>::finished, [this, watcher]() {
                try {
                    RpcResponse response = watcher->result();
                    if (response.success) {
                        m_logTextEdit->append("✓ Async RPC call successful: " + 
                                             QJsonDocument(response.result).toJson(QJsonDocument::Compact));
                        m_testResults.append("✓ Asynchronous RPC works");
                    } else {
                        m_logTextEdit->append("✗ Async RPC call failed: " + response.error);
                        m_testResults.append("✗ Asynchronous RPC failed");
                    }
                } catch (const std::exception& e) {
                    m_logTextEdit->append("✗ Async RPC result failed: " + QString::fromStdString(e.what()));
                    m_testResults.append("✗ Asynchronous RPC failed");
                }
                watcher->deleteLater();
            });
            
            watcher->setFuture(future);
            m_logTextEdit->append("Async RPC call initiated...");
            
        } catch (const std::exception& e) {
            m_logTextEdit->append("✗ Async RPC test failed: " + QString::fromStdString(e.what()));
            m_testResults.append("✗ Asynchronous RPC failed");
        }
    }
    
    /**
     * @brief 测试错误处理
     */
    void testErrorHandling() {
        m_logTextEdit->append("\n--- Test 4: Error Handling ---");
        
        try {
            // 测试不存在的服务
            RpcResponse response = CommunicationBus::instance()->callRpcSync(
                "NonExistentService", "testMethod", QJsonObject(), "Qt5Test", 1000);
            
            if (!response.success && response.error.contains("Service not found")) {
                m_logTextEdit->append("✓ Service not found error handled correctly");
                m_testResults.append("✓ Error handling works");
            } else {
                m_logTextEdit->append("✗ Service not found error not handled properly");
                m_testResults.append("✗ Error handling failed");
            }
            
            // 测试不存在的方法
            response = CommunicationBus::instance()->callRpcSync(
                "Qt5TestService", "nonExistentMethod", QJsonObject(), "Qt5Test", 1000);
            
            if (!response.success) {
                m_logTextEdit->append("✓ Unknown method error handled correctly");
            } else {
                m_logTextEdit->append("✗ Unknown method should have failed");
            }
            
        } catch (const std::exception& e) {
            m_logTextEdit->append("✗ Error handling test failed: " + QString::fromStdString(e.what()));
            m_testResults.append("✗ Error handling failed");
        }
    }
    
    /**
     * @brief 显示测试结果
     */
    void showTestResults() {
        m_logTextEdit->append("\n=== Test Results Summary ===");
        for (const QString& result : m_testResults) {
            m_logTextEdit->append(result);
        }
        
        int passedTests = 0;
        for (const QString& result : m_testResults) {
            if (result.startsWith("✓")) {
                passedTests++;
            }
        }
        
        m_logTextEdit->append(QString("\nTotal: %1/%2 tests passed").arg(passedTests).arg(m_testResults.size()));
        
        if (passedTests == m_testResults.size()) {
            m_logTextEdit->append("🎉 All tests passed! Qt5.14.2 compatibility confirmed.");
        } else {
            m_logTextEdit->append("⚠️  Some tests failed. Please check the implementation.");
        }
    }

protected:
    /**
     * @brief 处理RPC方法调用
     */
    QJsonObject processRpcMethod(const QString& method, const QJsonObject& params) override {
        QJsonObject result;
        
        if (method == "testMethod") {
            result["status"] = "success";
            result["receivedParam"] = params["testParam"];
            result["processedBy"] = "Qt5TestService";
            result["qt5Compatible"] = true;
            result["timestamp"] = QDateTime::currentDateTime().toString();
            
        } else if (method == "asyncTestMethod") {
            // 模拟异步处理
            QThread::msleep(100);
            result["status"] = "async_success";
            result["receivedParam"] = params["testParam"];
            result["processedBy"] = "Qt5TestService";
            result["processingTime"] = 100;
            result["qt5Compatible"] = true;
            
        } else {
            throw std::runtime_error("Unknown method: " + method.toStdString());
        }
        
        return result;
    }

private:
    void setupUI() {
        auto layout = new QVBoxLayout(this);
        
        layout->addWidget(new QLabel("Qt5.14.2 Communication Framework Compatibility Test"));
        
        m_logTextEdit = new QTextEdit();
        m_logTextEdit->setReadOnly(true);
        m_logTextEdit->setFont(QFont("Consolas", 9));
        layout->addWidget(m_logTextEdit);
        
        auto buttonLayout = new QHBoxLayout();
        
        auto runTestBtn = new QPushButton("Run Tests Again");
        auto clearLogBtn = new QPushButton("Clear Log");
        
        buttonLayout->addWidget(runTestBtn);
        buttonLayout->addWidget(clearLogBtn);
        buttonLayout->addStretch();
        
        layout->addLayout(buttonLayout);
        
        connect(runTestBtn, &QPushButton::clicked, [this]() {
            m_testResults.clear();
            runAutomaticTests();
        });
        
        connect(clearLogBtn, &QPushButton::clicked, m_logTextEdit, &QTextEdit::clear);
    }
    
    void setupTests() {
        // 订阅测试主题
        subscribeToTopic("qt5.test.message");
        subscribeToTopic("qt5.compatibility.test");
        
        m_logTextEdit->append("Qt5.14.2 Compatibility Test initialized");
        m_logTextEdit->append("Subscribed to test topics");
        m_logTextEdit->append("RPC service 'Qt5TestService' registered");
    }

private:
    QTextEdit* m_logTextEdit;
    QStringList m_testResults;
};

/**
 * @brief 主函数
 */
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << "Qt version:" << QT_VERSION_STR;
    qDebug() << "Starting Qt5.14.2 compatibility test...";
    
    Qt5CompatibilityTest test;
    test.show();
    
    return app.exec();
}

#include "qt5_compatibility_test.moc"
